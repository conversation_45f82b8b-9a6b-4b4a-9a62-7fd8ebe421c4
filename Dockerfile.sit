FROM golang:1.24.2 AS builder
WORKDIR /app
COPY . .
RUN go mod tidy
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o main ./cmd

FROM alpine:latest
RUN apk add --no-cache ca-certificates tzdata
WORKDIR /app
COPY --from=builder /app/main .

RUN if [ -f ".env.sit" ]; then cp .env.sit .env; else touch .env; fi
COPY ./conf ./conf

# Create cert directory for certificate files
RUN mkdir -p /app/cert

# Copy and setup entrypoint script
COPY ./entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

EXPOSE 8284
ENTRYPOINT ["/app/entrypoint.sh"]
