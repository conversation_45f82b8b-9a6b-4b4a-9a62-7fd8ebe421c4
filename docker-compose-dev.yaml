services:
  content-core-service:
    container_name: content-core-service
    build:
      context: .
      dockerfile: Dockerfile.dev
    # ports:
    #   - "8284:8284"
    volumes:
      - ./cert:/app/cert      
    networks:
      - auction-microservices_auctlive-net
    environment:
      - APP_ENV=siri
    restart: unless-stopped

networks:
  auction-microservices_auctlive-net:
    external: true
