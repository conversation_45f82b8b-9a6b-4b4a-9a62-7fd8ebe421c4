package config

import (
	"content-service/internal/global"
	"fmt"
	"log"
	"os"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

var Cfg global.Config

func LoadConfig() {

	env := os.Getenv("APP_ENV") // "local", "dev", "sit"
	log.Println("env :", env)
	if env == "" {
		env = "local"
	}
	viper.SetConfigName(fmt.Sprintf("config.%s", env))

	viper.SetConfigType("yaml")
	viper.AddConfigPath("conf")

	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("❌ Failed to read config file: %v", err)
	}

	viper.OnConfigChange(func(e fsnotify.Event) {
		log.Printf("🔄 Config file changed: %s", e.Name)
	})
	viper.WatchConfig()

	if err := viper.Unmarshal(&Cfg); err != nil {
		log.Fatalf("❌ Failed to unmarshal config into struct: %v", err)
	}

	log.Printf("✅ Config loaded for %s", Cfg.App.AppName)
}
