app :
  appName: "Content Service"
  domain: "content"
  httpPort: 8284
  allowOrigins: "http://localhost:4200, https://*************:4200,http://*************:8080,https://*************:4201"
public:
    - "api/signin"
dbConfig:
  host: auction-dev-db-micro
  port: 5432
  dbName: auction_core
  username: postgres
  password: "!1q2w3e4r"
service:
  pgwUrl:
  qrPayUrl:
redisConfig:
  host: auction-dev-redis-micro
  port: 6379
  password:
  db: 0
  protocol: 2
ErpConfig:
  token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************.BMMfNCw0dkw0Z0S0fB7Bg0O-5a8GJu7u3xBi8IwqaK8"
  eventUrl: "https://systemuat.auct.co.th:3443/api/v1/masterEvent/getEvent"
  assetGroupUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAssetGroup/getAssetGroup"
  assetTypeUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAssetType/getAssetType"
  saleChannelUrl: "https://systemuat.auct.co.th:3443/api/v1/masterSaleChanel/getSaleChanel"
  branchUrl: "https://systemuat.auct.co.th:3443/api/v1/masterBranch/getBranch"
  customerGroupUrl: "https://systemuat.auct.co.th:3443/api/v1/masterCustomerGroup/getCustomerGroup"
  customerTypeUrl: "https://systemuat.auct.co.th:3443/api/v1/masterCustomerType/getCustomerType"
  assetLocationFloorUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAssetLocationFloor/getAssetLocationFloor"
  reasonUrl: "https://systemuat.auct.co.th:3443/api/v1/masterReason/getReason"
  lotSouUrl: "https://systemuat.auct.co.th:3443/api/v1/lot/getlot"
  lotSouLotLineUrl: "https://systemuat.auct.co.th:3443/api/v1/lot/getLotLine"
  vatCodeUrl: "https://systemuat.auct.co.th:3443/api/v1/masterVatCode/getVatCode"
  regionUrl: "https://systemuat.auct.co.th:3443/api/v1/masterRegion/getRegion"
  costRevenueUrl: "https://systemuat.auct.co.th:3443/api/v1/masterCostRevenue/getCostRevenue"
  vatBusinessUrl: "https://systemuat.auct.co.th:3443/api/v1/masterVatBusiness/getVatBusiness"
  vendorUrl: "https://systemuat.auct.co.th:3443/api/v1/masterVendor/getVendor"
  vendorGroupUrl: "https://systemuat.auct.co.th:3443/api/v1/masterVendorGroup/getVendorGroup"
  countryUrl: "https://systemuat.auct.co.th:3443/api/v1/masterCountry/getCountry"
  districtUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAddress/getDistrict"
  subDistrictUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAddress/getSubDistrict"
  prefixNameUrl: "https://systemuat.auct.co.th:3443/api/v1/masterPrefixName/getPrefixName"
  cityUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAddress/getProvince"
  customerUrl: "https://systemuat.auct.co.th:3443/api/v1/masterCustomer/getCustomer"
  departmentUrl: "https://systemuat.auct.co.th:3443/api/v1/masterDepartment/getDepartment"
  postcodeUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAddress/getPostcode"
  holidayUrl: "https://systemuat.auct.co.th:3443/api/v1/masterHoliday/getHoliday"
  bankUrl: "https://systemuat.auct.co.th:3443/api/v1/masterBankAccount/getBankAccount"
  paymentMethodUrl: "https://systemuat.auct.co.th:3443/api/v1/masterPaymentMethod/getPaymentMethod"
  registerTypeUrl: "https://systemuat.auct.co.th:3443/api/v1/masterRegisterType/getRegisterType"
  registerTypeCarUrl: "https://systemuat.auct.co.th:3443/api/v1/masterRegisterTypeCar/getRegisterTypeCar"
  customerSellerOfferUrl: "https://systemuat.auct.co.th:3443/api/v1/masterSellerOffer/getCustomerSellerOffer"
  sellerOfferUrl: "https://systemuat.auct.co.th:3443/api/v1/masterSellerOffer/getSellerOffer"