package constant

import "backend-common-lib/constant"

var SortingPaymentDueNotification = map[string][]string{
	"days_before_due_th":  {"cfg.value_string2"},
	"days_before_due_en":  {"cfg.value_string"},
	"customer_group_desc": {"customer_group.description_th"},
}

var SortingAdditionalService = map[string][]string{
	"days_before_due_th": {"cfg.value_string2"},
	"days_before_due_en": {"cfg.value_string"},
	"start_date":         {"start_date", "end_date"},
	"end_date":           {"start_date", "end_date"},
}

var SortingPolicyConsent = map[string][]string{
	"version":       {"version_major", "version_minor"},
	"version_major": {"version_major", "version_minor"},
}

type DropdownItem struct {
	Value   string
	LabelTh string
	LabelEn string
}

var DropdownActionStatus = []DropdownItem{
	{Value: constant.ActionStatusWaitingEn, LabelTh: constant.ActionStatusWaitingTh, LabelEn: constant.ActionStatusWaitingEn},
	{Value: constant.ActionStatusApprovedEn, LabelTh: constant.ActionStatusApprovedTh, LabelEn: constant.ActionStatusApprovedEn},
	{Value: constant.ActionStatusRejectedEn, LabelTh: constant.ActionStatusRejectedTh, LabelEn: constant.ActionStatusRejectedEn},
}

const (
	PATH_PDPA                          = "pdpa"
	PATH_MARKETING                     = "marketing"
	PATH_PROXY_BID                     = "proxy-bid"
	PATH_WITHHOLDING_TAX               = "withholding-tax"
	CONSENT_TYPE_BUYER_PDPA            = "BUYER_PDPA"
	CONSENT_TYPE_BUYER_MARKETING       = "BUYER_MARKETING"
	CONSENT_TYPE_BUYER_PROXY_BID       = "BUYER_PROXY_BID"
	CONSENT_TYPE_BUYER_WITHHOLDING_TAX = "BUYER_WITHHOLDING_TAX"
)
