pipeline {
  agent {
    label 'agent-auction'
  }

  environment {
    ACTION_BRANCH = 'sit'
    HARBOR_HOST = 'harbor-npd.auct.local'
    PROJECT_NAME = 'auctlive-sit'
    IMAGE_NAME = 'content-service'
    HARBOR_CREDENTIAL = 'harbor-jenkin'
    KUBE_CREDENTIAL = 'kubeconfig-jenkins'
    DEPLOY_NAMESPACE = 'auction-sit'
  }

  stages {
    stage('Checkout') {
      steps {
        checkout([$class: 'GitSCM',
          branches: [[name: "*/${ACTION_BRANCH}"]],
          userRemoteConfigs: [[
            url: 'https://<EMAIL>/sirisoftgroup/AUCT-Revamp-Auctlive-Application/_git/auction-backend-content',
            credentialsId: 'azure-pat'
          ]]
        ])
        echo "Checked out branch: ${ACTION_BRANCH}"
      }
    }

    stage('Image Tag') {
      steps {
        script {
          def tag = sh(script: 'git rev-parse --short=7 HEAD', returnStdout: true).trim()
          env.IMAGE_TAG = tag
          echo "Set IMAGE_TAG=${env.IMAGE_TAG}"
        }
      }
    }

    stage('Clone Common Lib') {
      steps {
        dir('backend-common-lib') {
          git credentialsId: 'azure-pat',
            url: 'https://dev.azure.com/sirisoftgroup/AUCT-Revamp-Auctlive-Application/_git/auction-backend-common-lib',
            branch: 'main',
            changelog: false,
            poll: false
        }
      }
    }

    stage('Setup Certificates') {
      steps {
        script {
          sh 'mkdir -p cert'
          
          withCredentials([
            file(credentialsId: 'secret-sit', variable: 'SIT_PEM'),
            file(credentialsId: 'secret-sit-key', variable: 'SIT_KEY_PEM'),
            file(credentialsId: 'secret-erp_cert', variable: 'ERP_CERT_PEM')
          ]) {
            sh '''
              cp $SIT_PEM cert/sit.pem
              cp $SIT_KEY_PEM cert/sit-key.pem
              cp $ERP_CERT_PEM cert/erp_cert.crt
              chmod 644 cert/*.pem cert/*.crt || true
            '''
          }
        }
      }
    }

    stage('Build Image') {
      steps {
        withDockerRegistry(credentialsId: "${HARBOR_CREDENTIAL}", url: "https://${HARBOR_HOST}") {
          sh """
            export IMAGE_TAG=${IMAGE_TAG}
            docker compose -f docker-compose-${ACTION_BRANCH}.yaml build --pull ${IMAGE_NAME}
          """
        }
      }
    }

    stage('Push Image') {
      steps {
        withDockerRegistry(credentialsId: "${HARBOR_CREDENTIAL}", url: "https://${HARBOR_HOST}") {
          sh """
            export IMAGE_TAG=${IMAGE_TAG}
            docker compose -f docker-compose-${ACTION_BRANCH}.yaml push ${IMAGE_NAME}
          """
        }
      }
    }

    stage('Deploy to SIT') {
      steps {
        withCredentials([file(credentialsId: "${KUBE_CREDENTIAL}", variable: 'KUBECONFIG')]) {
          sh """
            export KUBECONFIG=$KUBECONFIG
            helm upgrade --install ${IMAGE_NAME} ./helm \
              -f helm/values-${ACTION_BRANCH}.yaml \
              --namespace ${DEPLOY_NAMESPACE} --create-namespace \
              --set image.repository=${HARBOR_HOST}/${PROJECT_NAME}/${IMAGE_NAME} \
              --set image.tag=${IMAGE_TAG}
          """
        }
      }
    }

    stage('Wait for Deployment') {
      steps {
        withCredentials([file(credentialsId: "${KUBE_CREDENTIAL}", variable: 'KUBECONFIG')]) {
          sh """
            export KUBECONFIG=$KUBECONFIG
            kubectl rollout status deployment/${IMAGE_NAME} -n ${DEPLOY_NAMESPACE} --timeout=300s
          """
        }
      }
    }

    stage('Copy Certificates to Pod') {
      steps {
        script {
          withCredentials([file(credentialsId: "${KUBE_CREDENTIAL}", variable: 'KUBECONFIG')]) {
            def podName = sh(script: """
              export KUBECONFIG=$KUBECONFIG
              kubectl get pods -n ${DEPLOY_NAMESPACE} -l app=${IMAGE_NAME} -o jsonpath='{.items[0].metadata.name}'
            """, returnStdout: true).trim()
            
            echo "Found pod: ${podName}"
            
            // Copy certificate files to the pod
            sh """
              export KUBECONFIG=$KUBECONFIG
              kubectl cp cert/sit.pem ${DEPLOY_NAMESPACE}/${podName}:/app/cert/sit.pem
              kubectl cp cert/sit-key.pem ${DEPLOY_NAMESPACE}/${podName}:/app/cert/sit-key.pem
              kubectl cp cert/erp_cert.crt ${DEPLOY_NAMESPACE}/${podName}:/app/cert/erp_cert.crt
            """
            
            // Verify certificates were copied
            sh """
              export KUBECONFIG=$KUBECONFIG
              kubectl exec ${podName} -n ${DEPLOY_NAMESPACE} -- ls -la /app/cert/
            """
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }
    success {
      echo "✅ Deployed: ${IMAGE_NAME}:${IMAGE_TAG} to ${DEPLOY_NAMESPACE}"
    }
    failure {
      echo "❌ Deploy failed"
    }
  }
}
