package dropdown

import (
	"backend-common-lib/constant"
	"backend-common-lib/model"
	"backend-common-lib/util"
	constants_content "content-service/constant"

	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"strconv"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/log"
)

func (s *dropdownService) GetAssetTypeDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.AssetTypeRepo.GetDB().Model(&entity.MasterAssetType{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var assetTypes []entity.MasterAssetType
	if err := query.Order("asset_type_code asc").Find(&assetTypes).Error; err != nil {
		return dropdowns, err
	}

	for _, assetType := range assetTypes {
		if assetType.AssetTypeCode == nil || assetType.DescriptionTh == nil || assetType.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      assetType.Id,
			Value:   *assetType.AssetTypeCode,
			LabelTh: *assetType.DescriptionTh,
			LabelEn: *assetType.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAssetGroupDropdown(isActive string, assetTypeCode string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.AssetGroupRepo.GetDB().Model(&entity.MasterAssetGroup{})
	if assetTypeCode != "" {
		query = query.Where("asset_type_code = ?", assetTypeCode)
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var assetGroups []entity.MasterAssetGroup
	if err := query.Order("asset_group_code asc").Find(&assetGroups).Error; err != nil {
		return dropdowns, err
	}

	for _, assetGroup := range assetGroups {
		if assetGroup.AssetGroupCode == nil || assetGroup.DescriptionTh == nil || assetGroup.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      assetGroup.Id,
			Value:   *assetGroup.AssetGroupCode,
			LabelTh: *assetGroup.DescriptionTh,
			LabelEn: *assetGroup.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAssetGroupDropdownByMultipleAssetType(isActive string, req dto.AssetGroupMultiSelectDropdownReqDto) ([]dto.DropdownAssetGroupDto, error) {
	var dropdowns []dto.DropdownAssetGroupDto

	query := s.AssetGroupRepo.GetDB().Model(&entity.MasterAssetGroup{}).
		Select("master_asset_group.*, master_asset_type.id as asset_type_id").
		Joins("JOIN master_asset_type ON master_asset_type.asset_type_code = master_asset_group.asset_type_code")
	if len(req.AssetTypeIds) != 0 {
		query = query.Where("master_asset_type.id IN ?", req.AssetTypeIds)
	}
	if isActive != "" {
		query = query.Where("master_asset_group.is_active = ?", isActive)
	}

	var assetGroups []entity.MasterAssetGroup
	if err := query.Order("asset_group_code asc").Find(&assetGroups).Error; err != nil {
		return dropdowns, err
	}

	for _, assetGroup := range assetGroups {
		if assetGroup.AssetGroupCode == nil || assetGroup.DescriptionTh == nil || assetGroup.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, dto.DropdownAssetGroupDto{
			Id:          assetGroup.Id,
			Value:       util.Val(assetGroup.AssetGroupCode),
			LabelTh:     util.Val(assetGroup.DescriptionTh),
			LabelEn:     util.Val(assetGroup.DescriptionEn),
			AssetTypeId: util.Val(assetGroup.AssetTypeId),
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetEventDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.EventRepo.GetDB().Model(&entity.MasterEvent{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var events []entity.MasterEvent
	if err := query.Order("event_code asc").Find(&events).Error; err != nil {
		return dropdowns, err
	}

	for _, event := range events {
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      event.Id,
			Value:   *event.EventCode,
			LabelTh: *event.DescriptionTh,
			LabelEn: *event.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetVendorDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.VendorRepo.GetDB().Model(&entity.MasterVendor{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var vendors []entity.MasterVendor
	if err := query.Order("vendor_no asc").Find(&vendors).Error; err != nil {
		return dropdowns, err
	}

	for _, vendor := range vendors {
		if vendor.VendorNo == nil || vendor.VendorName == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      vendor.Id,
			Value:   *vendor.VendorNo,
			LabelTh: *vendor.VendorName,
			LabelEn: *vendor.VendorName,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetVendorGroupDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.VendorGroupRepo.GetDB().Model(&entity.MasterVendorGroup{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var vendorGroups []entity.MasterVendorGroup
	if err := query.Order("vendor_group_code asc").Find(&vendorGroups).Error; err != nil {
		return dropdowns, err
	}

	for _, vendorGroup := range vendorGroups {
		if vendorGroup.VendorGroupCode == nil || vendorGroup.DescriptionTh == nil || vendorGroup.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      vendorGroup.Id,
			Value:   *vendorGroup.VendorGroupCode,
			LabelTh: *vendorGroup.DescriptionTh,
			LabelEn: *vendorGroup.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetSaleChannelDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.SaleChannelRepo.GetDB().Model(&entity.MasterSaleChannel{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var saleChannels []entity.MasterSaleChannel
	if err := query.Order("sale_channel_code asc").Find(&saleChannels).Error; err != nil {
		return dropdowns, err
	}

	for _, saleChannel := range saleChannels {
		if saleChannel.SaleChannelCode == nil || saleChannel.DescriptionTh == nil || saleChannel.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      saleChannel.Id,
			Value:   *saleChannel.SaleChannelCode,
			LabelTh: *saleChannel.DescriptionTh,
			LabelEn: *saleChannel.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetBranchDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.BranchRepo.GetDB().Model(&entity.MasterBranch{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var branches []entity.MasterBranch
	if err := query.Order("description_th asc").Find(&branches).Error; err != nil {
		return dropdowns, err
	}

	for _, branch := range branches {
		if branch.BranchCode == nil || branch.DescriptionTh == nil || branch.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      branch.Id,
			Value:   *branch.BranchCode,
			LabelTh: *branch.DescriptionTh,
			LabelEn: *branch.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetLotSettingDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.LotSettingRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name IN (?, ?, ?)", constant.ConfigParamConst.FLOOR_STATUS, constant.ConfigParamConst.CONFIG_FEATURE_LOT, constant.ConfigParamConst.SALE_CHANNEL)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetFloorStatusDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.FloorStatusRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.FLOOR_STATUS)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigFeatureLotDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.CONFIG_FEATURE_LOT)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetSaleChannelConfigDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.SaleChannelRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.SALE_CHANNEL)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAuctionStatusDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.AuctionStatusRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.AUCTION_STATUS)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetPrefixNameDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.PrefixNameRepo.GetDB().Model(&entity.MasterPrefixName{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var prefixNames []entity.MasterPrefixName
	if err := query.Order("prefix_name_code asc").Find(&prefixNames).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range prefixNames {
		if m.PrefixNameCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.PrefixNameCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetVatBusinessDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.VatBusinessRepo.GetDB().Model(&entity.MasterVatBusiness{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var vatBusinesses []entity.MasterVatBusiness
	if err := query.Order("vat_business_code asc").Find(&vatBusinesses).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range vatBusinesses {
		if m.VatBusinessCode == nil || m.Description == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.VatBusinessCode,
			LabelTh: *m.Description,
			LabelEn: *m.Description,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetDayBeforeDueDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.DayBeforeDueRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.DAYS_BEFORE_DUE)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetRegionDropdown(isActive string, countryCode string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.RegionRepo.GetDB().Model(&entity.MasterRegion{})
	if countryCode != "" {
		query = query.Where("country_code = ?", countryCode)
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var regions []entity.MasterRegion
	if err := query.Order("region_code asc").Find(&regions).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range regions {
		if m.RegionCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.RegionCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetFloorDropdown(isActive string, branchId string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.FloorRepo.GetDB().Model(&entity.MasterAssetLocationFloor{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	if branchId != "" {
		branchIdInt, err := strconv.Atoi(branchId)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		branchResult, err := s.BranchRepo.FindById(branchIdInt)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		if branchResult != nil {
			query = query.Where("branch_code = ?", branchResult.BranchCode)
		}
	}

	var floors []entity.MasterAssetLocationFloor
	if err := query.Order("floor asc").Find(&floors).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range floors {
		if m.LocationFloorCode == nil || m.DescriptionTH == nil || m.DescriptionEN == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.Floor,
			LabelTh: "ลาน " + *m.Floor,
			LabelEn: "Yard " + *m.Floor,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetVatCodeDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.VatCodeRepo.GetDB().Model(&entity.MasterVatCode{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var vatCodes []entity.MasterVatCode
	if err := query.Order("vat_code asc").Find(&vatCodes).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range vatCodes {
		if m.VatCode == nil || m.Description == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.VatCode,
			LabelTh: *m.Description,
			LabelEn: *m.Description,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetDistrictDropdown(isActive string, cityCode string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.DistrictRepo.GetDB().Model(&entity.MasterDistrict{})
	if cityCode != "" {
		query = query.Where("city_code = ?", cityCode)
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var districts []entity.MasterDistrict
	if err := query.Order("district_code asc").Find(&districts).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range districts {
		if m.DistrictCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.DistrictCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetCityDropdown(isActive string, regionCode string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.CityRepo.GetDB().Model(&entity.MasterCity{})
	if regionCode != "" {
		query = query.Where("region_code = ?", regionCode)
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var cities []entity.MasterCity
	if err := query.Order("city_code asc").Find(&cities).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range cities {
		if m.CityCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.CityCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetNationalityDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.NationalityRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.NATIONALITY)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetSubDistrictDropdown(isActive string, districtCode string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.SubDistrictRepo.GetDB().Model(&entity.MasterSubDistrict{})
	if districtCode != "" {
		query = query.Where("district_code = ?", districtCode)
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var subDistricts []entity.MasterSubDistrict
	if err := query.Order("sub_district_code asc").Find(&subDistricts).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range subDistricts {
		if m.SubDistrictCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.SubDistrictCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetCustomerTypeDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.CustomerTypeRepo.GetDB().Model(&entity.MasterCustomerType{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var customerTypes []entity.MasterCustomerType
	if err := query.Order("customer_type_code asc").Find(&customerTypes).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range customerTypes {
		if m.CustomerTypeCode == nil || m.DescriptionTh == nil {
			continue
		}

		labelEn := ""
		if m.DescriptionEn != nil {
			labelEn = *m.DescriptionEn
		} else {
			labelEn = *m.DescriptionTh
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.CustomerTypeCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: labelEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetCustomerGroupDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.CustomerGroupRepo.GetDB().Model(&entity.MasterCustomerGroup{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var customerGroups []entity.MasterCustomerGroup
	if err := query.Order("customer_group_code asc").Find(&customerGroups).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range customerGroups {
		if m.CustomerGroupCode == nil || m.DescriptionTh == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.CustomerGroupCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionTh,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetDepartmentDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.DepartmentRepo.GetDB().Model(&entity.MasterDepartment{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var departments []entity.MasterDepartment
	if err := query.Order("department_code asc").Find(&departments).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range departments {
		if m.DepartmentCode == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.DepartmentCode,
			LabelTh: *m.DepartmentNameTh,
			LabelEn: *m.DepartmentNameEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAuctionNameDropdown(c *fiber.Ctx) ([]model.AuctionDropdownDto, error) {
	var dropdowns []model.AuctionDropdownDto

	query := s.AuctionNameRepo.GetDB().Model(&entity.Auction{}).Preload("Event")

	query = query.Where("is_active = ?", true)

	auctionId := c.Query("auctionId", "")

	if auctionId != "" {
		auctionIdInt, err := strconv.Atoi(auctionId)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		query = query.Where("auction_type_id = ?", auctionIdInt)

	}

	var auctionNames []entity.Auction
	if err := query.Order("auction_name asc").Find(&auctionNames).Error; err != nil {
		return dropdowns, err
	}

	if len(auctionNames) == 0 {
		return dropdowns, nil
	}

	for _, m := range auctionNames {
		labelTh := m.AuctionName
		labelEn := ""
		labelEn = m.Description

		dropdowns = append(dropdowns, model.AuctionDropdownDto{
			Id:      m.Id,
			Value:   m.AuctionName,
			LabelTh: labelTh,
			LabelEn: labelEn,
			Event:   *m.Event.DescriptionTh,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetBuyerDropdown(c *fiber.Ctx) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	isActive := c.Query("isActive", "")
	customerGroupId := c.Query("customerGroupId", "")

	query := s.BuyerRepo.GetDB().Model(&entity.Buyer{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	if customerGroupId != "" {
		customerGroupIdInt, err := strconv.Atoi(customerGroupId)
		if err != nil {
			log.Error("Invalid customer group ID:", err)
			return dropdowns, err
		}
		query = query.Where("customer_group_id = ?", customerGroupIdInt)
	}

	var buyers []entity.Buyer
	if err := query.Order("bidder_id asc").Find(&buyers).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range buyers {
		if m.BidderId == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.BidderId,
			LabelTh: *m.BidderId,
			LabelEn: *m.BidderId,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetProxyBidStatusDropdown(c *fiber.Ctx) ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigParameterRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.DROPDOWN_PROXY_BID_STATUS)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetActionStatusDropdown(c *fiber.Ctx) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	for _, actionStatus := range constants_content.DropdownActionStatus {
		dropdowns = append(dropdowns, model.DropdownDto{
			Value:   actionStatus.Value,
			LabelTh: actionStatus.LabelTh,
			LabelEn: actionStatus.LabelEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetProxyBidCancelReasonDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.ProxyBidCancelReasonRepo.GetDB().Model(&entity.ProxyBidCancelReason{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var proxyBidCancelReason []entity.ProxyBidCancelReason
	if err := query.Order("reason asc").Find(&proxyBidCancelReason).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range proxyBidCancelReason {
		if m.Reason == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.Reason,
			LabelTh: *m.Reason,
			LabelEn: *m.Reason,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetRoleDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.AssetTypeRepo.GetDB().Model(&entity.Role{})

	var roles []entity.Role
	if err := query.Order("name asc").Find(&roles).Error; err != nil {
		return dropdowns, err
	}

	for _, role := range roles {

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      role.Id,
			Value:   role.Name,
			LabelTh: role.Name,
			LabelEn: role.Name,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigAuctionCollateralDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.AUCTION_COLLATERAL)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigDisplayLocationDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.DISPLAY_LOCATION)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigCampaignEventTypeDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.CAMPAIGN_EVENT_TYPE)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigAuctionTypeDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.AUCTION_TYPE)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigServiceTypeDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.SERVICE_TYPE)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetHelpRequestStatusDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.DROPDOWN_HELP_REQUEST_STATUS)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetHelpRequestReasonDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.HelpRequestReasonRepo.GetDB().Model(&entity.HelpRequestReason{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var helpRequestReason []entity.HelpRequestReason
	if err := query.Order("reason asc").Find(&helpRequestReason).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range helpRequestReason {
		if m.Reason == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.Reason,
			LabelTh: *m.Reason,
			LabelEn: *m.Reason,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetReprintSlipReasonDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.ReprintSlipReasonRepo.GetDB().Model(&entity.ReprintSlipReason{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var reprintSlipReason []entity.ReprintSlipReason
	if err := query.Order("reason asc").Find(&reprintSlipReason).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range reprintSlipReason {
		if m.Reason == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.Reason,
			LabelTh: *m.Reason,
			LabelEn: *m.Reason,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAuctionBidVoidReasonDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.DROPDOWN_AUCTION_BID_VOID_REASON_STATUS)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}
