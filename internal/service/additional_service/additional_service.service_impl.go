package service

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"encoding/json"
	"log"
	"net/http"
)

func (s *additionalServiceService) GetAdditionalService(req model.PagingRequest) (model.PagingModel[dto.AdditionalServiceDto], error) {
	resp := model.PagingModel[dto.AdditionalServiceDto]{}

	//NOTE - Get List
	result, err := s.Repo.FindAllAdditionalService(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountAllAdditionalService()
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Get Top 4 Buyer of each record
	var ids []int
	for _, n := range result {
		ids = append(ids, n.BaseEntity.Id)
	}

	assetMap, err := s.ServiceAssetRepo.GetAssetByAdditionalServiceIds(ids)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	customerGroupMap, err := s.ServiceCustomerGroup.GetCustomerGroupByAdditionalServiceIds(ids)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	providerMap, err := s.ServiceProviderRepo.GetProviderByAdditionalServiceIds(ids)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	// displayMap, err := s.ServiceAssetRepo.GetDisplayByAdditionalServiceIds(ids)
	// if err != nil {
	// 	return resp, errs.NewError(http.StatusInternalServerError, err)
	// }

	byte2, _ := json.MarshalIndent(assetMap, "", "")
	log.Println("🚀" + string(byte2))

	// //NOTE - Map to DTO
	dtoList := make([]dto.AdditionalServiceDto, 0, len(result))
	for _, entity := range result {
		dtoResult := dto.AdditionalServiceDto{}
		byte, _ := json.MarshalIndent(entity, "", "")
		log.Println("🚀" + string(byte))
		dtoResult = util.MapToWithCreatedByAndUpdatedBy[dto.AdditionalServiceDto](entity)

		for _, asset := range assetMap[entity.BaseEntity.Id] {
			if asset.AssetType != nil && asset.AssetGroup == nil {

				dtoResult.ServiceAssetType = append(dtoResult.ServiceAssetType, &dto.ServiceAssetType{
					AssetTypeId:     asset.AssetTypeId,
					AssetTypeDescTh: util.Ptr(asset.AssetType.DescriptionTh),
					AssetTypeDescEn: util.Ptr(asset.AssetType.DescriptionEn),
				})
			} else if asset.AssetGroup != nil {
				dtoResult.ServiceAssetGroup = append(dtoResult.ServiceAssetGroup, &dto.ServiceAssetGroup{
					AssetGroupId:     asset.AssetGroupId,
					AssetGroupDescTh: util.Ptr(asset.AssetGroup.DescriptionTh),
					AssetGroupDescEn: util.Ptr(asset.AssetGroup.DescriptionEn),
				})
			}
		}
		for _, customerGroup := range customerGroupMap[entity.BaseEntity.Id] {
			if customerGroup.CustomerGroup != nil {

				dtoResult.ServiceCustomerGroup = append(dtoResult.ServiceCustomerGroup, &dto.ServiceCustomerGroup{
					CustomerGroupId:   customerGroup.CustomerGroupId,
					CustomerGroupDesc: util.Ptr(customerGroup.CustomerGroup.DescriptionTh),
				})
			}
		}
		for _, provider := range providerMap[entity.BaseEntity.Id] {

			dtoResult.ServiceProvider = append(dtoResult.ServiceProvider, &dto.ServiceProvider{
				Id:           provider.ID,
				ProviderName: provider.ProviderName,
				ProviderLink: provider.ProviderLink,
			})

		}
		dtoList = append(dtoList, dtoResult)

	}

	//NOTE - Response
	resp = util.Val(util.MapPaginationResult(dtoList, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit))

	return resp, nil
}

// func (s *additionalServiceService) GetAdditionalServiceByID(id int) (dto.AdditionalServiceDto, error) {
// 	resp := dto.AdditionalServiceDto{}
// 	result, err := s.Repo.FindAdditionalServiceByID(id)
// 	if err != nil {
// 		if errs.IsGormNotFound(err) {
// 			return dto.AdditionalServiceDto{}, errs.NewError(http.StatusNotFound, err)
// 		}
// 		return dto.AdditionalServiceDto{}, errs.NewError(http.StatusInternalServerError, err)
// 	}

// 	//NOTE - Map to DTO
// 	resp = util.MapToWithCreatedByAndUpdatedBy[dto.AdditionalServiceDto](result)
// 	return resp, nil
// }

// func validateAdditionalService(additionalService dto.AdditionalServiceReqDto) error {
// 	if additionalService.Remark == nil || util.Val(additionalService.Remark) == "" {
// 		return errs.NewError(http.StatusBadRequest, fmt.Errorf("remark is required"))
// 	}
// 	return nil
// }

// func (s *additionalServiceService) CreateAdditionalService(req dto.AdditionalServiceReqDto) error {
// 	now := util.Now()

// 	//NOTE - Check Required fields
// 	err := validateAdditionalService(req)
// 	if err != nil {
// 		return err
// 	}

// 	//NOTE - Check for duplicate customer_group_id
// 	exist, err := s.Repo.FindByRemark(util.Val(req.Remark), req.Id)
// 	if err == nil && exist != nil {
// 		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "remark is duplicated", "error.additionalService.duplicateRemark")
// 	} else if err != nil && !errs.IsGormNotFound(err) {
// 		return errs.NewError(http.StatusInternalServerError, err)
// 	}

// 	//NOTE - Create Payment Due Notification
// 	entityAdditionalService := util.MapToPtr[entity.AdditionalService](req)
// 	if entityAdditionalService == nil {
// 		return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "Invalid request data", "")
// 	}

// 	entityAdditionalService.BaseEntity = &model.BaseEntity{
// 		CreatedBy:   req.ActionBy,
// 		CreatedDate: now,
// 		UpdatedBy:   req.ActionBy,
// 		UpdatedDate: &now,
// 	}

// 	if err := s.Repo.InsertAdditionalService(entityAdditionalService); err != nil {
// 		return errs.NewError(http.StatusInternalServerError, err)
// 	}
// 	return nil
// }

// func (s *additionalServiceService) UpdateAdditionalService(req dto.AdditionalServiceReqDto) error {
// 	//NOTE - Check Required fields
// 	if err := validateAdditionalService(req); err != nil {
// 		return errs.NewError(http.StatusBadRequest, err)
// 	}

// 	//NOTE - check duplicate
// 	exist, err := s.Repo.FindByRemark(util.Val(req.Remark), req.Id)
// 	if err == nil && exist != nil {
// 		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "remark is duplicated", "error.additionalService.duplicateRemark")
// 	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
// 		return errs.NewError(http.StatusInternalServerError, err)
// 	}

// 	now := util.Now()
// 	fieldsToUpdate := map[string]interface{}{
// 		"remark":       req.Remark,
// 		"is_active":    req.IsActive,
// 		"updated_by":   req.ActionBy,
// 		"updated_date": &now,
// 	}

// 	affectedRows, err := s.Repo.UpdateAdditionalService(req.Id, fieldsToUpdate)
// 	if err != nil {
// 		return errs.NewError(http.StatusInternalServerError, err)
// 	}
// 	if affectedRows == 0 {
// 		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
// 	}
// 	return nil
// }

// func (s *additionalServiceService) UpdateAdditionalServiceStatus(req dto.AdditionalServiceReqDto) error {
// 	fieldsToUpdate := map[string]interface{}{
// 		"is_active":    req.IsActive,
// 		"updated_by":   req.ActionBy,
// 		"updated_date": util.Now(),
// 	}

// 	affectedRows, err := s.Repo.UpdateAdditionalService(req.Id, fieldsToUpdate)
// 	if err != nil {
// 		return errs.NewError(http.StatusInternalServerError, err)
// 	}
// 	if affectedRows == 0 {
// 		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
// 	}

// 	return nil
// }

// func (s *additionalServiceService) DeleteAdditionalService(id int) error {
// 	affectedRows, err := s.Repo.DeleteAdditionalService(id)
// 	if err != nil {
// 		return errs.NewError(http.StatusInternalServerError, err)
// 	}
// 	if affectedRows == 0 {
// 		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", id), "")
// 	}
// 	return nil
// }
