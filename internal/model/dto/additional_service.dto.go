package dto

import (
	"backend-common-lib/model"
	"time"
)

type AdditionalServiceDto struct {
	model.BaseDto
	ServiceName          *string                 `json:"serviceName"`
	Detail               *string                 `json:"detail"`
	ServiceTypeTh        *string                 `json:"serviceTypeTh"`
	ServiceTypeEn        *string                 `json:"serviceTypeEn"`
	ServiceTypeId        *int                    `json:"serviceTypeId"`
	StartDate            *time.Time              `json:"startDate"`
	EndDate              *time.Time              `json:"endDate"`
	ServiceAssetType     []*ServiceAssetType     `json:"serviceAssetType"`
	ServiceAssetGroup    []*ServiceAssetGroup    `json:"serviceAssetGroup"`
	ServiceCustomerGroup []*ServiceCustomerGroup `json:"serviceCustomerGroup"`
	ServiceProvider      []*ServiceProvider      `json:"serviceProvider"`
	ServiceDisplay       []*ServiceDisplay       `json:"serviceDisplay"`
	IsActive             bool                    `json:"isActive"`
}

type ServiceAssetType struct {
	AssetTypeId     *int    `json:"assetTypeId"`
	AssetTypeDescTh *string `json:"assetTypeDescTh"`
	AssetTypeDescEn *string `json:"assetTypeDescEn"`
}

type ServiceAssetGroup struct {
	AssetGroupId     *int    `json:"assetGroupId"`
	AssetGroupDescTh *string `json:"assetGroupDescTh"`
	AssetGroupDescEn *string `json:"assetGroupDescEn"`
}

type ServiceCustomerGroup struct {
	CustomerGroupId   *int    `json:"customerGroupId"`
	CustomerGroupDesc *string `json:"customerGroupDesc"`
}

type ServiceProvider struct {
	ProviderName *string `json:"providerName"`
	ProviderLink *string `json:"providerLink"`
}

type ServiceDisplay struct {
	DisplayId     *int    `json:"displayId"`
	DisplayDescTh *string `json:"displayDescTh"`
	DisplayDescEn *string `json:"displayDescEn"`
}
