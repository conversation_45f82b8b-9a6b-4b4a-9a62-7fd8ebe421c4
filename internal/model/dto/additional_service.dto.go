package dto

import (
	"backend-common-lib/model"
	"time"
)

type AdditionalServiceDto struct {
	model.BaseDto
	ServiceName          *string                 `json:"serviceName"`
	Detail               *string                 `json:"detail"`
	ServiceTypeTh        *string                 `json:"serviceTypeTh"`
	ServiceTypeEn        *string                 `json:"serviceTypeEn"`
	ServiceTypeId        *int                    `json:"serviceTypeId"`
	StartDate            *time.Time              `json:"startDate"`
	EndDate              *time.Time              `json:"endDate"`
	ServiceAssetType     []*ServiceAssetType     `json:"serviceAssetType"`
	ServiceAssetGroup    []*ServiceAssetGroup    `json:"serviceAssetGroup"`
	ServiceCustomerGroup []*ServiceCustomerGroup `json:"serviceCustomerGroup"`
	ServiceProvider      []*ServiceProvider      `json:"serviceProvider"`
	ServiceDisplay       []*ServiceDisplay       `json:"serviceDisplay"`
	IsActive             bool                    `json:"isActive"`
}

type ServiceAssetType struct {
	Id              *int    `json:"id"`
	AssetTypeId     *int    `json:"assetTypeId"`
	AssetTypeDescTh *string `json:"assetTypeDescTh"`
	AssetTypeDescEn *string `json:"assetTypeDescEn"`
}

type ServiceAssetGroup struct {
	Id               *int    `json:"id"`
	AssetGroupId     *int    `json:"assetGroupId"`
	AssetGroupDescTh *string `json:"assetGroupDescTh"`
	AssetGroupDescEn *string `json:"assetGroupDescEn"`
}

type ServiceCustomerGroup struct {
	Id                *int    `json:"id"`
	CustomerGroupId   *int    `json:"customerGroupId"`
	CustomerGroupDesc *string `json:"customerGroupDesc"`
}

type ServiceProvider struct {
	Id           *int    `json:"id"`
	ProviderName *string `json:"providerName"`
	ProviderLink *string `json:"providerLink"`
}

type ServiceDisplay struct {
	Id            *int    `json:"id"`
	DisplayId     *int    `json:"displayId"`
	DisplayDescTh *string `json:"displayDescTh"`
	DisplayDescEn *string `json:"displayDescEn"`
}
