package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterCustomer struct {
	*model.BaseEntity
	CompanyCode          *string                `json:"companyCode"`
	CustomerNo           *string                `json:"customerNo" ignore:"true"`
	CustomerGroup        *string                `json:"customerGroup"`
	PrefixNameCode       *string                `json:"prefixNameCode"`
	CustomerName         *string                `json:"customerName"`
	VatRegistrationNo    *string                `json:"vatRegistrationNo"`
	TaxBranch            *string                `json:"taxBranch"`
	BlackList            bool                   `json:"blackList"`
	BlackListDate        *time.Time             `json:"blackListDate"`
	BlackListDescription *string                `json:"blackListDescription"`
	Blocked              bool                   `json:"blocked"`
	CustomerType         *string                `json:"customerType"`
	PhoneNo              *string                `json:"phoneNo"`
	SMSPhoneNo           *string                `json:"smsPhoneNo"`
	Email                *string                `json:"email"`
	DayOfBirth           *time.Time             `json:"dayOfBirth"`
	CustomerGrade        *string                `json:"customerGrade"`
	CustomerCreditGroup  *string                `json:"customerCreditGroup"`
	TypeOfDelivery       *string                `json:"typeOfDelivery"`
	CustomerAccGroup     *string                `json:"customerAccGroup"`
	RoomNo               *string                `json:"roomNo"`
	Address              *string                `json:"address"`
	Address2             *string                `json:"address2"`
	Building             *string                `json:"building"`
	Floor                *string                `json:"floor"`
	HouseNo              *string                `json:"houseNo"`
	MooNo                *string                `json:"mooNo"`
	Village              *string                `json:"village"`
	Alley                *string                `json:"alley"`
	Street               *string                `json:"street"`
	SubDistrict          *string                `json:"subDistrict"`
	District             *string                `json:"district"`
	Country              *string                `json:"country"`
	City                 *string                `json:"city"`
	PostCode             *string                `json:"postCode"`
	Nationality          *string                `json:"nationality"`
	BidderId             *string                `json:"bidderId"`
	ObjectiveOfAuction   *string                `json:"objectiveOfAuction"`
	IsActive             bool                   `json:"isActive"`
	IsDeletedByErp       bool                   `json:"isDeletedByErp" ignore:"true"`
	LatestSyncDate       *time.Time             `json:"latestSyncDate" ignore:"true"`
	CreatedUser          *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser          *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterCustomer) TableName() string {
	return "master_customer"
}
