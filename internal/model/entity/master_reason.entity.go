package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterReason struct {
	*model.BaseEntity
	CompanyCode           *string                `column:"company_code" json:"companyCode"`
	ReasonCode            *string                `column:"reason_code" json:"reasonCode"`
	DescriptionTh         *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn         *string                `column:"description_en" json:"descriptionEn"`
	SaleProcessStatusCode *string                `column:"sale_process_status_code" json:"saleProcessStatusCode"`
	Type                  *string                `column:"type" json:"type"`
	Page                  *string                `column:"page" json:"page"`
	SellerPaymentOption   *string                `column:"seller_payment_option" json:"sellerPaymentOption"`
	IsActive              bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp        bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate        *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser           *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser           *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterReason) TableName() string {
	return "master_reason"
}
