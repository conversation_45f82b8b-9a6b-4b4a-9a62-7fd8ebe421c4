package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterCustomerType struct {
	*model.BaseEntity
	CustomerTypeCode *string                `column:"customer_type_code" json:"customerTypeCode" example:"Buyer"`
	DescriptionTh    *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn    *string                `column:"description_en" json:"descriptionEn"`
	IsWHT            bool                   `column:"is_wht" json:"isWHT"`
	IsActive         bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp   bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate   *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser      *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser      *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterCustomerType) TableName() string {
	return "master_customer_type"
}
