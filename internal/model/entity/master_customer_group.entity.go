package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterCustomerGroup struct {
	*model.BaseEntity
	CustomerGroupCode *string                `column:"customer_group_code" json:"customerGroupCode"`
	DescriptionTh     *string                `column:"description_th" json:"descriptionTh"`
	IsActive          bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp    bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate    *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser       *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser       *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterCustomerGroup) TableName() string {
	return "master_customer_group"
}
