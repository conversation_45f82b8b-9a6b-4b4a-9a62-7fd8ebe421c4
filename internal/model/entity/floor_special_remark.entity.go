package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type FloorSpecialRemark struct {
	*model.BaseEntity
	Remark      *string                `column:"remark" json:"remark"`
	IsActive    bool                   `column:"is_active" json:"isActive"`
	CreatedUser *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
	DeletedUser *model.EmployeeForJoin `gorm:"foreignKey:DeletedBy;references:ID"`
	DeletedBy   *int                   `column:"deleted_by" json:"deletedBy" ignore:"true"`
	DeletedAt   *gorm.DeletedAt        `gorm:"column:deleted_at" json:"deletedAt"`
}

func (FloorSpecialRemark) TableName() string {
	return "floor_special_remark"
}
