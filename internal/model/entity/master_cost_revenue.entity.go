package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterCostRevenue struct {
	*model.BaseEntity
	CompanyCode           *string                `column:"company_code" json:"companyCode"`
	CostRevenueCode       *string                `column:"cost_revenue_code" json:"costRevenueCode"`
	DescriptionTh         *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn         *string                `column:"description_en" json:"descriptionEn"`
	ReportingGroup        *string                `column:"reporting_group" json:"reportingGroup"`
	IsRefund              bool                   `column:"is_refund" json:"isRefund"`
	IsPriceIncludingVat   bool                   `column:"is_price_including_vat" json:"isPriceIncludingVat"`
	VatCode               *string                `column:"vat_code" json:"vatCode"`
	WhtProdCode           *string                `column:"wht_prod_code" json:"whtProdCode"`
	Type                  *string                `column:"type" json:"Type"`
	GroupHeader           bool                   `column:"group_header" json:"groupHeader"`
	InterfaceGuid         *string                `column:"interface_guid" json:"interfaceGuid"`
	InterfaceStatusCreate *string                `column:"interface_status_create" json:"interfaceStatusCreate"`
	InterfaceStatusUpdate *string                `column:"interface_status_update" json:"interfaceStatusUpdate"`
	Wht3Percent           *int                   `column:"wht3_percent" json:"wht3Percent"`
	UnitPrice             bool                   `column:"unit_price" json:"UnitPrice"`
	ApiVatCode            *string                `column:"api_vat_code" json:"apiVatCode"`
	ExcludeSendAr         bool                   `column:"exclude_send_ar" json:"excludeSendAr"`
	IsActive              bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp        bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate        *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser           *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser           *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterCostRevenue) TableName() string {
	return "master_cost_revenue"
}
