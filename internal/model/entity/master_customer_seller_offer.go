package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterCustomerSellerOffer struct {
	*model.BaseEntity
	CustomerNo     *string                `column:"customer_no" json:"customerNo"`
	SellerCode     *string                `column:"seller_code" json:"sellerCode"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	CompanyCode    *string                `column:"company_code" json:"companyCode"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterCustomerSellerOffer) TableName() string {
	return "master_customer_seller_offer"
}
