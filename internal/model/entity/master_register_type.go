package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterRegisterType struct {
	*model.BaseEntity
	AttributeCode  *string                `column:"attribute_code" json:"attributeCode"`
	LineNo         *int                   `column:"line_no" json:"lineNo"`
	OptionTh       *string                `column:"option_th" json:"optionTh"`
	OptionEn       *string                `column:"option_en" json:"optionEn"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	CompanyCode    *string                `column:"company_code" json:"companyCode"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterRegisterType) TableName() string {
	return "master_register_type"
}
