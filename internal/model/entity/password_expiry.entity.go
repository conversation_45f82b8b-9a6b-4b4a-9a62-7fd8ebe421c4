package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type PasswordExpiry struct {
	*model.BaseEntity
	UserType               *string                `column:"user_type" json:"userType"`
	PasswordExpiryDays     *int                   `column:"password_expiry_days" json:"passwordExpiryDays"`
	PinExpiryDays          *int                   `column:"pin_expiry_days" json:"pinExpiryDays"`
	NotifyBeforeExpiryDays *int                   `column:"notify_before_expiry_days" json:"notifyBeforeExpiryDays"`
	CreatedUser            *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser            *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
	DeletedUser            *model.EmployeeForJoin `gorm:"foreignKey:DeletedBy;references:ID"`
	DeletedBy              *int                   `column:"deleted_by" json:"deletedBy" ignore:"true"`
	DeletedAt              *gorm.DeletedAt        `gorm:"column:deleted_at" json:"deletedAt"`
}

func (PasswordExpiry) TableName() string {
	return "password_expiry"
}
