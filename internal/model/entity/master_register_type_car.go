package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterRegisterTypeCar struct {
	*model.BaseEntity
	AssetRegisterTypeCode *string                `column:"asset_register_type_code" json:"assetRegisterTypeCode"`
	DescriptionTh         *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn         *string                `column:"description_en" json:"descriptionEn"`
	IsActive              bool                   `column:"is_active" json:"isActive"`
	CompanyCode           *string                `column:"company_code" json:"companyCode"`
	IsDeletedByErp        bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate        *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser           *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser           *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterRegisterTypeCar) TableName() string {
	return "master_register_type_car"
}
