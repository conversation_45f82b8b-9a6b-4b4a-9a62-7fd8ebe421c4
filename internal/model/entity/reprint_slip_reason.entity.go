package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type ReprintSlipReason struct {
	*model.BaseEntity
	Reason      *string                `column:"reason" json:"reason"`
	IsActive    bool                   `column:"is_active" json:"isActive"`
	CreatedUser *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
	DeletedUser *model.EmployeeForJoin `gorm:"foreignKey:DeletedBy;references:ID"`
	DeletedBy   *int                   `column:"deleted_by" json:"deletedBy" ignore:"true"`
	DeletedAt   *gorm.DeletedAt        `gorm:"column:deleted_at" json:"deletedAt"`
}

func (ReprintSlipReason) TableName() string {
	return "reprint_slip_reason"
}
