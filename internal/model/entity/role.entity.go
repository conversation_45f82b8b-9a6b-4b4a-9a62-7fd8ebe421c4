package entity

import (
	"backend-common-lib/model"
	"time"
)

type Role struct {
	*model.BaseEntity
	RoleName        string                 `gorm:"column:role_name" json:"roleName"`
	RoleDescription *string                `gorm:"column:role_description" json:"roleDescription"`
	IsActive        bool                   `gorm:"column:is_active" json:"isActive"`
	IsSensitiveData bool                   `gorm:"column:is_sensitive_data" json:"isSensitiveData"`
	DeletedDate     *time.Time             `gorm:"column:deleted_date" json:"deletedDate"`
	CreatedUser     *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser     *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (Role) TableName() string {
	return "role"
}
