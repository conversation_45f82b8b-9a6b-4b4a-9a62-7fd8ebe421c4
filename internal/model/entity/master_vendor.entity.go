package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterVendor struct {
	*model.BaseEntity
	PrefixNameCode    *string                `column:"prefix_name_code" json:"prefixNameCode"`
	VendorNo          *string                `column:"vendor_no" json:"vendorNo"`
	VendorName        *string                `column:"vendor_name" json:"vendorName"`
	VendorGroup       *string                `column:"vendor_group" json:"vendorGroup"`
	VatRegistrationNo *string                `column:"vat_registration_no" json:"vatRegistrationNo"`
	IsActive          bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp    bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate    *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser       *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser       *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterVendor) TableName() string {
	return "master_vendor"
}
