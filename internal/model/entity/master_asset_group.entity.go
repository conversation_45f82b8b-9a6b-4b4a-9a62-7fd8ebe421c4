package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterAssetGroup struct {
	*model.BaseEntity
	AssetGroupCode *string                `column:"asset_group_code" json:"assetGroupCode"`
	AssetTypeCode  *string                `column:"asset_type_code" json:"assetTypeCode"`
	DescriptionTh  *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn  *string                `column:"description_en" json:"descriptionEn"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	CompanyCode    *string                `column:"company_code" json:"companyCode"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterAssetGroup) TableName() string {
	return "master_asset_group"
}
