package entity

import (
	"time"
)

type ApprovalStatusEnum string

const (
	ApprovalStatusPending  ApprovalStatusEnum = "PENDING"
	ApprovalStatusApproved ApprovalStatusEnum = "APPROVED"
	ApprovalStatusRejected ApprovalStatusEnum = "REJECTED"
)

type BuyerRegistrationRequest struct {
	ID             int                `gorm:"primaryKey;column:id" json:"id"`
	BuyerID        int                `gorm:"column:buyer_id;not null" json:"buyer_id"`
	IsActive       bool               `gorm:"column:is_active;not null" json:"is_active"`
	IsDeleted      bool               `gorm:"column:is_deleted;not null" json:"is_deleted"`
	CreatedDate    time.Time          `gorm:"column:created_date;not null" json:"created_date"`
	CreatedBy      int                `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedDate    time.Time          `gorm:"column:updated_date;not null" json:"updated_date"`
	UpdatedBy      int                `gorm:"column:updated_by;not null" json:"updated_by"`
	RequestDate    *time.Time         `gorm:"column:request_date" json:"request_date"`
	ApprovalStatus ApprovalStatusEnum `gorm:"column:approval_status;type:approval_status_enum;default:PENDING;not null" json:"approval_status"`
}

func (BuyerRegistrationRequest) TableName() string {
	return "buyer_registration_request"
}
