package member

import (
	buyerRepository "content-service/internal/repository/buyer"
	service "content-service/internal/service/member"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB) {
	buyerRepo := buyerRepository.NewBuyerRepository(db)
	service := service.NewMemberService(buyerRepo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("member")
	route.Post("/search", h.SearchMemberWithFilter)
	route.Put("/status/:id", h.UpdateMemberStatus)
}
