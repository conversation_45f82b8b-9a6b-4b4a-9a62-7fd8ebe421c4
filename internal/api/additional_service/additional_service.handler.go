package additionalservice

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	service "content-service/internal/service/additional_service"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service service.AdditionalServiceService
}

func (h *Handler) GetAdditionalService(c *fiber.Ctx) error {
	var req model.PagingRequest

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.GetAdditionalService(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAdditionalServiceByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.GetAdditionalServiceByID(id)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

// func (h *Handler) CreateAdditionalService(c *fiber.Ctx) error {
// 	var req dto.AdditionalServiceReqDto

// 	if err := c.BodyParser(&req); err != nil {
// 		return errs.NewError(http.StatusBadRequest, err)
// 	}

// 	err := h.Service.CreateAdditionalService(req)
// 	if err != nil {
// 		return err
// 	}
// 	return c.Status(http.StatusOK).JSON(nil)
// }

// func (h *Handler) UpdateAdditionalService(c *fiber.Ctx) error {
// 	var req dto.AdditionalServiceReqDto

// 	if err := c.BodyParser(&req); err != nil {
// 		return errs.NewError(http.StatusBadRequest, err)
// 	}

// 	idStr := c.Params("id")
// 	id, err := strconv.Atoi(idStr)
// 	if err != nil {
// 		return errs.NewError(http.StatusBadRequest, err)
// 	}
// 	req.Id = id

// 	err = h.Service.UpdateAdditionalService(req)
// 	if err != nil {
// 		return err
// 	}
// 	return c.Status(http.StatusOK).JSON(nil)
// }

// func (h *Handler) UpdateAdditionalServiceStatus(c *fiber.Ctx) error {
// 	var req dto.AdditionalServiceReqDto

// 	if err := c.BodyParser(&req); err != nil {
// 		return errs.NewError(http.StatusBadRequest, err)
// 	}

// 	idStr := c.Params("id")
// 	id, err := strconv.Atoi(idStr)
// 	if err != nil {
// 		return errs.NewError(http.StatusBadRequest, err)
// 	}
// 	req.Id = id

// 	err = h.Service.UpdateAdditionalServiceStatus(req)
// 	if err != nil {
// 		return err
// 	}
// 	return c.Status(http.StatusOK).JSON(nil)
// }

// func (h *Handler) DeleteAdditionalService(c *fiber.Ctx) error {
// 	idStr := c.Params("id")
// 	id, err := strconv.Atoi(idStr)
// 	if err != nil {
// 		return errs.NewError(http.StatusBadRequest, err)
// 	}

// 	err = h.Service.DeleteAdditionalService(id)
// 	if err != nil {
// 		return err
// 	}
// 	return c.Status(http.StatusOK).JSON(nil)
// }
