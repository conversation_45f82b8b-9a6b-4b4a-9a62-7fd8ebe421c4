package additionalservice

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/additional_service"
	serviceAssetRepo "content-service/internal/repository/additional_service_asset"
	serviceCustomerGroupRepo "content-service/internal/repository/additional_service_customer_group"
	serviceDisplayRepo "content-service/internal/repository/additional_service_display"
	serviceProviderRepo "content-service/internal/repository/additional_service_provider"
	service "content-service/internal/service/additional_service"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewAdditionalServiceRepository(db)
	serviceAssetRepo := serviceAssetRepo.NewAdditionalServiceAssetRepository(db)
	serviceCustomerGroupRepo := serviceCustomerGroupRepo.NewAdditionalServiceCustomerGroupRepository(db)
	serviceProviderRepo := serviceProviderRepo.NewAdditionalServiceProviderRepository(db)
	serviceDisplayRepo := serviceDisplayRepo.NewAdditionalServiceDisplayRepository(db)
	service := service.NewAdditionalServiceService(repo, serviceAssetRepo, serviceCustomerGroupRepo, serviceProviderRepo, serviceDisplayRepo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("additional-services")
	route.Post("/search", h.GetAdditionalService)
	// route.Get("/:id", h.GetAdditionalServiceByID)
	// route.Post("/", h.CreateAdditionalService)
	// route.Put("/:id", h.UpdateAdditionalService)
	// route.Put("/status/:id", h.UpdateAdditionalServiceStatus)
	// route.Delete("/:id", h.DeleteAdditionalService)
}
