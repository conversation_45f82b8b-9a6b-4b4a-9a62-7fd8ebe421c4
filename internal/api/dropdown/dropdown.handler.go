package dropdown

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	dropdownService "content-service/internal/service/dropdown"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service dropdownService.DropdownService
}

func (h *Handler) GetAssetTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAssetTypeDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAssetGroupDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAssetGroupDropdown(c.Query("isActive"), c.Query("assetTypeCode"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAssetGroupDropdownByMultipleAssetType(c *fiber.Ctx) error {
	var req dto.AssetGroupMultiSelectDropdownReqDto

	if err := c.BodyParser(&req); err != nil {
		return err
	}

	res, err := h.Service.GetAssetGroupDropdownByMultipleAssetType(c.Query("isActive"), req)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetRegionDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetRegionDropdown(c.Query("isActive"), c.Query("countryCode"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetEventDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetEventDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetVendorDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetVendorDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetVendorGroupDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetVendorGroupDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetSaleChannelDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetSaleChannelDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetBranchDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetBranchDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetLotSettingDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetLotSettingDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetFloorStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetFloorStatusDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigFeatureLotDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigFeatureLotDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetSaleChannelConfigDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetSaleChannelConfigDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAuctionStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAuctionStatusDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetPrefixNameDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetPrefixNameDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetVatBusinessDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetVatBusinessDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetDayBeforeDueDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetDayBeforeDueDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetFloorDropdown(c *fiber.Ctx) error {

	res, err := h.Service.GetFloorDropdown(c.Query("isActive"), c.Query("branchId"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetVatCodeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetVatCodeDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetDistrictDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetDistrictDropdown(c.Query("isActive"), c.Query("cityCode"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetCityDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetCityDropdown(c.Query("isActive"), c.Query("regionCode"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetNationalityDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetNationalityDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetSubDistrictDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetSubDistrictDropdown(c.Query("isActive"), c.Query("districtCode"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetCustomerTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetCustomerTypeDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetCustomerGroupDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetCustomerGroupDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetDepartmentDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetDepartmentDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAuctionNameDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAuctionNameDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetBuyerDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetBuyerDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetProxyBidStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetProxyBidStatusDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetActionStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetActionStatusDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetProxyBidCancelReasonDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetProxyBidCancelReasonDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetRoleDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetRoleDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigAuctionCollateralDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigAuctionCollateralDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigDisplayLocationDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigDisplayLocationDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigCampaignEventTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigCampaignEventTypeDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigAuctionTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigAuctionTypeDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigAdditionalServiceTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigServiceTypeDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetHelpRequestStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetHelpRequestStatusDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetHelpRequestReasonDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetHelpRequestReasonDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetReprintSlipReasonDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetReprintSlipReasonDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAuctionBidVoidReasonDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAuctionBidVoidReasonDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
