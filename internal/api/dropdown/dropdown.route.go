package dropdown

import (
	"content-service/internal/global"
	auctionAssetRepo "content-service/internal/repository/auction_asset"
	auctionNameRepo "content-service/internal/repository/auction_name"
	buyerRepo "content-service/internal/repository/buyer"
	config_parameter "content-service/internal/repository/config_parameter"
	assetGroupRepo "content-service/internal/repository/master_asset_group"
	yardRepo "content-service/internal/repository/master_asset_location_floor"
	assetTypeRepo "content-service/internal/repository/master_asset_type"
	auctionTypeRepo "content-service/internal/repository/master_auction_type"
	branchRepo "content-service/internal/repository/master_branch"
	cityRepo "content-service/internal/repository/master_city"
	customerGroupRepo "content-service/internal/repository/master_customer_group"
	customerTypeRepo "content-service/internal/repository/master_customer_type"
	departmentRepo "content-service/internal/repository/master_department"
	districtRepo "content-service/internal/repository/master_district"
	eventRepo "content-service/internal/repository/master_event"
	prefixNameRepo "content-service/internal/repository/master_prefix_name"
	regionRepo "content-service/internal/repository/master_region"
	saleChannelRepo "content-service/internal/repository/master_sale_channel"
	subDistrictRepo "content-service/internal/repository/master_sub_district"
	vatBusinessRepo "content-service/internal/repository/master_vat_business"
	vatCodeRepo "content-service/internal/repository/master_vat_code"
	vendorRepo "content-service/internal/repository/master_vendor"
	vendorGroupRepo "content-service/internal/repository/master_vendor_group"
	proxyBidCancelReasonRepo "content-service/internal/repository/proxy_bid_cancel_reason"
	dropdownService "content-service/internal/service/dropdown"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	assetTypeRepository := assetTypeRepo.NewMasterAssetTypeRepository(db)
	assetGroupRepository := assetGroupRepo.NewMasterAssetGroupRepository(db)
	eventRepository := eventRepo.NewMasterEventRepository(db)
	vendorRepository := vendorRepo.NewMasterVendorRepository(db)
	vendorGroupRepository := vendorGroupRepo.NewMasterVendorGroupRepository(db)
	saleChannelRepository := saleChannelRepo.NewMasterSaleChannelRepository(db)
	branchRepository := branchRepo.NewMasterBranchRepository(db)
	lotSettingRepository := config_parameter.NewConfigParameterRepository(db)
	floorStatusRepository := config_parameter.NewConfigParameterRepository(db)
	configFeatureLotRepository := config_parameter.NewConfigParameterRepository(db)
	saleChannelConfigRepository := config_parameter.NewConfigParameterRepository(db)
	auctionStatusRepository := config_parameter.NewConfigParameterRepository(db)
	prefixNameRepository := prefixNameRepo.NewMasterPrefixNameRepository(db)
	vatBusinessRepository := vatBusinessRepo.NewMasterVatBusinessRepository(db)
	dayBeforeDueRepository := config_parameter.NewConfigParameterRepository(db)
	regionRepository := regionRepo.NewMasterRegionRepository(db)
	yardRepository := yardRepo.NewMasterAssetLocationFloorRepository(db)
	vatCodeRepository := vatCodeRepo.NewMasterVatCodeRepository(db)
	districtRepository := districtRepo.NewMasterDistrictRepository(db)
	cityRepository := cityRepo.NewMasterCityRepository(db)
	nationalityRepository := config_parameter.NewConfigParameterRepository(db)
	subDistrictRepository := subDistrictRepo.NewMasterSubDistrictRepository(db)
	customerTypeRepository := customerTypeRepo.NewMasterCustomerTypeRepository(db)
	customerGroupRepository := customerGroupRepo.NewMasterCustomerGroupRepository(db)
	departmentRepository := departmentRepo.NewMasterDepartmentRepository(db)
	auctionTypeRepository := auctionTypeRepo.NewMasterAuctionTypeRepository(db)
	auctionNameRepository := auctionNameRepo.NewAuctionNameRepository(db)
	buyerRepository := buyerRepo.NewBuyerRepository(db)
	configParameterRepository := config_parameter.NewConfigParameterRepository(db)
	proxyBidCancelReasonRepository := proxyBidCancelReasonRepo.NewProxyBidCancelReasonRepository(db)
	auctionAssetRepository := auctionAssetRepo.NewAuctionAssetRepository(db)

	service := dropdownService.NewDropdownService(
		assetTypeRepository, assetGroupRepository,
		eventRepository, vendorRepository,
		vendorGroupRepository, saleChannelRepository,
		branchRepository, lotSettingRepository,
		floorStatusRepository, configFeatureLotRepository,
		saleChannelConfigRepository, auctionStatusRepository,
		prefixNameRepository, vatBusinessRepository, dayBeforeDueRepository,
		regionRepository, yardRepository,
		vatCodeRepository, districtRepository,
		cityRepository, nationalityRepository,
		subDistrictRepository, customerTypeRepository,
		customerGroupRepository, departmentRepository,
		auctionTypeRepository, auctionNameRepository,
		buyerRepository, configParameterRepository,
		proxyBidCancelReasonRepository,
		auctionAssetRepository,
	)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(route fiber.Router, h *Handler) {

	route.Get("asset-types", func(c *fiber.Ctx) error {
		return h.GetAssetTypeDropdown(c)
	})

	route.Get("asset-groups", func(c *fiber.Ctx) error {
		return h.GetAssetGroupDropdown(c)
	})

	route.Get("regions", func(c *fiber.Ctx) error {
		return h.GetRegionDropdown(c)
	})

	route.Get("events", func(c *fiber.Ctx) error {
		return h.GetEventDropdown(c)
	})

	route.Get("vendors", func(c *fiber.Ctx) error {
		return h.GetVendorDropdown(c)
	})

	route.Get("vendor-groups", func(c *fiber.Ctx) error {
		return h.GetVendorGroupDropdown(c)
	})

	route.Get("sale-channels", func(c *fiber.Ctx) error {
		return h.GetSaleChannelDropdown(c)
	})

	route.Get("branches", func(c *fiber.Ctx) error {
		return h.GetBranchDropdown(c)
	})

	route.Get("lot-settings", func(c *fiber.Ctx) error {
		return h.GetLotSettingDropdown(c)
	})

	route.Get("floor-status", func(c *fiber.Ctx) error {
		return h.GetFloorStatusDropdown(c)
	})

	route.Get("configs-feature-lot", func(c *fiber.Ctx) error {
		return h.GetConfigFeatureLotDropdown(c)
	})

	route.Get("sale-channel-configs", func(c *fiber.Ctx) error {
		return h.GetSaleChannelConfigDropdown(c)
	})

	route.Get("auction-status", func(c *fiber.Ctx) error {
		return h.GetAuctionStatusDropdown(c)
	})

	route.Get("prefix-names", func(c *fiber.Ctx) error {
		return h.GetPrefixNameDropdown(c)
	})

	route.Get("vat-businesses", func(c *fiber.Ctx) error {
		return h.GetVatBusinessDropdown(c)
	})

	route.Get("days-before-due", func(c *fiber.Ctx) error {
		return h.GetDayBeforeDueDropdown(c)
	})

	route.Get("regions", func(c *fiber.Ctx) error {
		return h.GetRegionDropdown(c)
	})

	route.Get("floors", func(c *fiber.Ctx) error {
		return h.GetFloorDropdown(c)
	})

	route.Get("vat-codes", func(c *fiber.Ctx) error {
		return h.GetVatCodeDropdown(c)
	})

	route.Get("districts", func(c *fiber.Ctx) error {
		return h.GetDistrictDropdown(c)
	})

	route.Get("cities", func(c *fiber.Ctx) error {
		return h.GetCityDropdown(c)
	})

	route.Get("nationalities", func(c *fiber.Ctx) error {
		return h.GetNationalityDropdown(c)
	})

	route.Get("sub-districts", func(c *fiber.Ctx) error {
		return h.GetSubDistrictDropdown(c)
	})

	route.Get("customer-types", func(c *fiber.Ctx) error {
		return h.GetCustomerTypeDropdown(c)
	})

	route.Get("customer-groups", func(c *fiber.Ctx) error {
		return h.GetCustomerGroupDropdown(c)
	})

	route.Get("departments", func(c *fiber.Ctx) error {
		return h.GetDepartmentDropdown(c)
	})

	route.Get("auction-names", func(c *fiber.Ctx) error {
		return h.GetAuctionNameDropdown(c)
	})

	route.Get("buyers", func(c *fiber.Ctx) error {
		return h.GetBuyerDropdown(c)
	})

	route.Get("proxy-bid-status", func(c *fiber.Ctx) error {
		return h.GetProxyBidStatusDropdown(c)
	})

	route.Get("action-status", func(c *fiber.Ctx) error {
		return h.GetActionStatusDropdown(c)
	})

	route.Get("proxy-bid-cancel-reasons", func(c *fiber.Ctx) error {
		return h.GetProxyBidCancelReasonDropdown(c)
	})

	route.Get("roles", func(c *fiber.Ctx) error {
		return h.GetRoleDropdown(c)
	})

	route.Get("auction-collateral", func(c *fiber.Ctx) error {
		return h.GetConfigAuctionCollateralDropdown(c)
	})

	route.Get("display-location", func(c *fiber.Ctx) error {
		return h.GetConfigDisplayLocationDropdown(c)
	})

	route.Get("campaign-event-type", func(c *fiber.Ctx) error {
		return h.GetConfigCampaignEventTypeDropdown(c)
	})

	route.Get("auction-type", func(c *fiber.Ctx) error {
		return h.GetConfigAuctionTypeDropdown(c)
	})

	route.Get("help-requests-status", func(c *fiber.Ctx) error {
		return h.GetHelpRequestStatusDropdown(c)
	})
}
