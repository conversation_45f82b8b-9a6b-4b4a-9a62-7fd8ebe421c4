package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type additionalServiceCustomerGroupRepositoryImpl struct {
	DB *gorm.DB
}

type AdditionalServiceCustomerGroupRepository interface {
	GetCustomerGroupByAdditionalServiceIds(additionalServiceIds ...int) (map[int][]entity.AdditionalServiceCustomerGroup, error)
	GetDB() *gorm.DB
}

func NewAdditionalServiceCustomerGroupRepository(db *gorm.DB) AdditionalServiceCustomerGroupRepository {
	return &additionalServiceCustomerGroupRepositoryImpl{DB: db}
}
