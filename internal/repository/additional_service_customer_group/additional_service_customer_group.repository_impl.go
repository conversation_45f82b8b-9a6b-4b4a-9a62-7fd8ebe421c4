package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *additionalServiceCustomerGroupRepositoryImpl) GetCustomerGroupByAdditionalServiceIds(additionalServiceIds ...int) (map[int][]entity.AdditionalServiceCustomerGroup, error) {
	var assets []entity.AdditionalServiceCustomerGroup
	if err := r.DB.
		Preload("CustomerGroup").
		Where("additional_service_id IN ?", additionalServiceIds).Find(&assets).Error; err != nil {
		return nil, err
	}

	result := make(map[int][]entity.AdditionalServiceCustomerGroup)
	for _, asset := range assets {
		result[util.Val(asset.AdditionalServiceId)] = append(result[util.Val(asset.AdditionalServiceId)], asset)
	}

	return result, nil
}

func (r *additionalServiceCustomerGroupRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
