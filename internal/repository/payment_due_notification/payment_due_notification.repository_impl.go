package repository

import (
	"backend-common-lib/constant"
	"backend-common-lib/model"
	"backend-common-lib/util"
	contentConst "content-service/constant"
	"content-service/internal/model/entity"
	"fmt"

	"gorm.io/gorm"
)

func (r *paymentDueNotificationRepositoryImpl) buildPaymentDueNotificationQuery() *gorm.DB {
	query := r.DB.Model(&entity.PaymentDueNotification{}).
		Select(`payment_due_notification.*, 
		customer_group.description_th AS customer_group_desc,
		cfg.value_string AS days_before_due_th,
		cfg.value_string2 AS days_before_due_en`)
	query = util.JoinUsers("payment_due_notification")(query)
	query = query.
		Joins("LEFT JOIN master_customer_group AS customer_group ON customer_group.id = payment_due_notification.customer_group_id").
		Joins(`LEFT JOIN config_parameters AS cfg ON 
		cfg.parameter_module = ? AND 
		cfg.parameter_name = ? AND 
		cfg.value_int = payment_due_notification.days_before_due`,
			constant.ConfigParamConst.MODULE_SYSTEM_ADMIN,
			constant.ConfigParamConst.DAYS_BEFORE_DUE,
		)

	return query
}

func (r *paymentDueNotificationRepositoryImpl) FindAllPaymentDueNotification(req model.PagingRequest) ([]entity.PaymentDueNotification, error) {
	var results []entity.PaymentDueNotification

	query := r.buildPaymentDueNotificationQuery()
	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplySortFromMapField(query, snakeSort, req.SortOrder, "payment_due_notification", contentConst.SortingPaymentDueNotification, true)
	}

	//NOTE - default order
	query.Order(fmt.Sprintf("%s %s", "customer_group.description_th", "asc"))

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *paymentDueNotificationRepositoryImpl) CountAllPaymentDueNotification() (int64, error) {
	var count int64
	query := r.buildPaymentDueNotificationQuery()
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *paymentDueNotificationRepositoryImpl) FindPaymentDueNotificationByID(id int) (*entity.PaymentDueNotification, error) {
	var result entity.PaymentDueNotification

	query := r.buildPaymentDueNotificationQuery()
	query = query.Where("payment_due_notification.id = ?", id)

	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *paymentDueNotificationRepositoryImpl) InsertPaymentDueNotification(entityPaymentDue *entity.PaymentDueNotification) error {
	if err := r.DB.Create(entityPaymentDue).Error; err != nil {
		return err
	}
	return nil
}

func (r *paymentDueNotificationRepositoryImpl) FindByCustomerGroupId(customerGroupId int) ([]entity.PaymentDueNotification, error) {
	var results []entity.PaymentDueNotification

	query := r.DB.Where("payment_due_notification.customer_group_id = ?", customerGroupId)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *paymentDueNotificationRepositoryImpl) UpdatePaymentDueNotification(id int, fieldToUpdate map[string]interface{}) (int64, error) {
	result := r.DB.Model(&entity.PaymentDueNotification{}).
		Where("id = ?", id).
		Updates(fieldToUpdate)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *paymentDueNotificationRepositoryImpl) DeletePaymentDueNotification(id int) (int64, error) {
	result := r.DB.Where("id = ?", id).Delete(&entity.PaymentDueNotification{})

	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *paymentDueNotificationRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
