package repository

import (
	"backend-common-lib/model"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type additionalServiceRepositoryImpl struct {
	DB *gorm.DB
}

type AdditionalServiceRepository interface {
	FindAllAdditionalService(req model.PagingRequest) ([]entity.AdditionalService, error)
	CountAllAdditionalService() (int64, error)

	FindAdditionalServiceByID(id int) (*entity.AdditionalService, error)

	// InsertAdditionalService(entityPaymentDue *entity.AdditionalService) error
	// FindByCustomerGroupId(customerGroupId int) ([]entity.AdditionalService, error)

	// UpdateAdditionalService(id int, fieldToUpdate map[string]interface{}) (int64, error)

	// DeleteAdditionalService(id int) (int64, error)

	GetDB() *gorm.DB
}

func NewAdditionalServiceRepository(db *gorm.DB) AdditionalServiceRepository {
	return &additionalServiceRepositoryImpl{DB: db}
}
