package repository

import (
	"backend-common-lib/constant"
	"backend-common-lib/util"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *additionalServiceDisplayRepositoryImpl) GetDisplayByAdditionalServiceIds(serviceIds ...int) (map[int][]entity.AdditionalServiceDisplay, error) {
	var displays []entity.AdditionalServiceDisplay
	if err := r.DB.
		Select(`additional_service_display.*, 
		cfg.value_string AS display_desc_th,
		cfg.value_string2 AS display_desc_en`).
		Joins(`LEFT JOIN config_parameters AS cfg ON 
		cfg.parameter_module = ? AND 
		cfg.parameter_name = ? AND 
		cfg.value_int = additional_service_display.display_id`,
			constant.ConfigParamConst.MODULE_SYSTEM_ADMIN,
			constant.ConfigParamConst.DISPLAY_LOCATION,
		).
		Where("additional_service_id IN ?", serviceIds).
		Find(&displays).Error; err != nil {
		return nil, err
	}

	result := make(map[int][]entity.AdditionalServiceDisplay)
	for _, display := range displays {
		result[util.Val(display.AdditionalServiceId)] = append(result[util.Val(display.AdditionalServiceId)], display)
	}

	return result, nil
}

func (r *additionalServiceDisplayRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
