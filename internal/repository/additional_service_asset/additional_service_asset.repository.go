package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type additionalServiceAssetRepositoryImpl struct {
	DB *gorm.DB
}

type AdditionalServiceAssetRepository interface {
	GetAssetByAdditionalServiceIds(additionalServiceIds ...int) (map[int][]entity.AdditionalServiceAsset, error)
	GetDB() *gorm.DB
}

func NewAdditionalServiceAssetRepository(db *gorm.DB) AdditionalServiceAssetRepository {
	return &additionalServiceAssetRepositoryImpl{DB: db}
}
