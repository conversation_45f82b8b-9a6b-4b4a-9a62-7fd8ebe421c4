# Content Service Helm Chart

This Helm chart deploys the Auction Backend Content Service to Kubernetes.

## Chart Structure

```
helm/
├── Chart.yaml                 # Chart metadata
├── values.yaml               # Default values
├── values-preprod.yaml       # Pre-production environment values
├── values-prod.yaml          # Production environment values
├── values-sit.yaml           # SIT environment values
├── values-uat.yaml           # UAT environment values
├── .helmignore               # Files to exclude from packaging
├── README.md                 # This file
└── templates/
    ├── _helpers.tpl          # Template helper functions
    ├── deployment.yaml       # Kubernetes Deployment
    ├── service.yaml          # Kubernetes Service
    └── NOTES.txt             # Post-installation notes
```

## Configuration

### Service Configuration
- **Service Name**: content-service
- **Port**: 8184 (matches the application configuration)
- **Service Type**: ClusterIP

### Environment Variables
- `ENVIRONMENT`: Set to the deployment environment (development, sit, uat, preprod, prod)
- `GIN_MODE`: Set to "debug" for development, "release" for production environments

### Autoscaling
Autoscaling is configured differently per environment:
- **Development**: Disabled
- **SIT/UAT**: Disabled (manual scaling)
- **Preprod**: Enabled (1-5 replicas)
- **Production**: Enabled (2-10 replicas)

## Usage

### Install the chart
```bash
# For development
helm install content-service ./helm

# For specific environment
helm install content-service ./helm -f helm/values-prod.yaml
```

### Upgrade the chart
```bash
helm upgrade content-service ./helm -f helm/values-prod.yaml
```

### Uninstall the chart
```bash
helm uninstall content-service
```

## Values

| Parameter | Description | Default |
|-----------|-------------|---------|
| `replicaCount` | Number of replicas | `1` |
| `image.repository` | Container image repository | `content-service` |
| `image.tag` | Container image tag | `latest` |
| `image.pullPolicy` | Image pull policy | `IfNotPresent` |
| `service.port` | Service port | `8184` |
| `service.targetPort` | Container port | `8184` |
| `autoscaling.enabled` | Enable autoscaling | `false` |
| `autoscaling.minReplicas` | Minimum replicas | `1` |
| `autoscaling.maxReplicas` | Maximum replicas | `10` |
| `env.ENVIRONMENT` | Environment name | `development` |
| `env.GIN_MODE` | Gin framework mode | `debug` |

## Recent Changes

- Updated service name from `auth-service` to `content-service`
- Updated port configuration from `9000` to `8184` to match application
- Added environment-specific configurations
- Added autoscaling configuration
- Updated image repositories for different environments
- Added proper Helm chart structure with helpers and notes
