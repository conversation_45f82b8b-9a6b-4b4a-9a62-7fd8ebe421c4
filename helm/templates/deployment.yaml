apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "content-service.fullname" . }}
  labels:
    app: {{ include "content-service.name" . }}
spec:
  revisionHistoryLimit: 5
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ include "content-service.name" . }}
  template:
    metadata:
      labels:
        app: {{ include "content-service.name" . }}
    spec:
      serviceAccountName: default
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- range .Values.imagePullSecrets }}
        - name: {{ .name }}
        {{- end }}
      {{- end }}
      containers:
        - name: content-service
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.service.targetPort }}
          env:
            - name: GIN_MODE
              value: {{ .Values.env.GIN_MODE | default "release" | quote }}
            - name: ENVIRONMENT
              value: {{ .Values.env.ENVIRONMENT | quote }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
