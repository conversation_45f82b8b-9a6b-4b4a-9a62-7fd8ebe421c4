Thank you for installing {{ .Chart.Name }}.

Your release is named {{ .Release.Name }}.

To learn more about the release, try:

  $ helm status {{ .Release.Name }}
  $ helm get all {{ .Release.Name }}

The content service is now running on port {{ .Values.service.port }}.

You can access the service using:
  kubectl port-forward svc/{{ include "content-service.fullname" . }} {{ .Values.service.port }}:{{ .Values.service.port }}

Then visit http://localhost:{{ .Values.service.port }} in your browser.
